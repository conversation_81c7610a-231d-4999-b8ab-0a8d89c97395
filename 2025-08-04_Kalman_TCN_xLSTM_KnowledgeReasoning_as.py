#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Author: 汪祥
# @Time : 2025/08/04 18:29:53
# @File : 202584.py
# @Note : 卡尔曼滤波、多物理量、TCN、XLSTM、知识推理
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler, RobustScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error
from scipy import stats
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, TensorDataset
import seaborn as sns
from matplotlib import font_manager, rcParams
from scipy.signal import stft, welch
import time


# 设置中文字体
font_path = '/System/Library/Fonts/Hiragino Sans GB.ttc'
font_prop = font_manager.FontProperties(fname=font_path, size=12)
rcParams['font.family'] = font_prop.get_name()
rcParams['axes.unicode_minus'] = False

# 设置随机种子保证可复现性
np.random.seed(42)
torch.manual_seed(42)

# 1. 改进的卡尔曼滤波器实现 ----------------------------------------------------------
class AdaptiveKalmanFilter:
    def __init__(self, initial_variance=1.0, min_process_variance=1e-6, max_process_variance=1e-2, 
                 min_measurement_variance=1e-4, max_measurement_variance=1.0, innovation_threshold=3.0):
        """
        改进的自适应卡尔曼滤波器
        :param initial_variance: 初始方差估计
        :param min_process_variance: 最小过程噪声方差
        :param max_process_variance: 最大过程噪声方差
        :param min_measurement_variance: 最小测量噪声方差
        :param max_measurement_variance: 最大测量噪声方差
        :param innovation_threshold: 创新值异常检测阈值
        """
        self.min_process_variance = min_process_variance
        self.max_process_variance = max_process_variance
        self.min_measurement_variance = min_measurement_variance
        self.max_measurement_variance = max_measurement_variance
        self.innovation_threshold = innovation_threshold
        
        # 初始状态
        self.posteri_estimate = 0.0
        self.posteri_error_estimate = initial_variance
        self.innovation_history = []
        self.measurement_variance_history = []
        self.process_variance_history = []
        
    def adaptive_variance(self, innovation, prev_measurement_var):
        """
        自适应调整噪声方差
        """
        # 基于创新值调整测量噪声方差
        abs_innovation = np.abs(innovation)
        
        # 动态调整测量噪声
        if abs_innovation > self.innovation_threshold:
            # 创新值过大，增加测量噪声（降低对当前测量的信任）
            measurement_variance = min(prev_measurement_var * 1.5, self.max_measurement_variance)
        else:
            # 创新值正常，缓慢减小测量噪声
            measurement_variance = max(prev_measurement_var * 0.95, self.min_measurement_variance)
        
        # 过程噪声与测量噪声保持比例关系
        process_variance = max(min(measurement_variance * 0.01, self.max_process_variance), self.min_process_variance)
        
        return process_variance, measurement_variance
    
    def update(self, measurement, prev_measurement_var):
        """
        更新卡尔曼滤波器状态
        :param measurement: 当前测量值
        :param prev_measurement_var: 上一时刻的测量噪声方差
        :return: (滤波后的值, 卡尔曼增益, 过程噪声方差, 测量噪声方差)
        """
        # 预测步骤
        priori_estimate = self.posteri_estimate
        priori_error_estimate = self.posteri_error_estimate
        
        # 计算创新值（测量残差）
        innovation = measurement - priori_estimate
        
        # 自适应调整噪声参数
        process_variance, measurement_variance = self.adaptive_variance(innovation, prev_measurement_var)
        
        # 更新步骤
        kalman_gain = priori_error_estimate / (priori_error_estimate + measurement_variance)
        self.posteri_estimate = priori_estimate + kalman_gain * innovation
        self.posteri_error_estimate = (1 - kalman_gain) * priori_error_estimate + process_variance
        
        # 保存历史记录
        self.innovation_history.append(innovation)
        self.measurement_variance_history.append(measurement_variance)
        self.process_variance_history.append(process_variance)
        
        return self.posteri_estimate, kalman_gain, process_variance, measurement_variance

def apply_adaptive_kalman_filter(signal, initial_measurement_var=0.1, innovation_threshold=3.0):
    """
    应用自适应卡尔曼滤波，并返回滤波信号和状态特征
    """
    kf = AdaptiveKalmanFilter(initial_variance=1.0, innovation_threshold=innovation_threshold)
    filtered_signal = np.zeros_like(signal)
    kalman_gains = np.zeros_like(signal)
    process_variances = np.zeros_like(signal)
    measurement_variances = np.zeros_like(signal)
    
    # 使用初始测量方差
    measurement_var = initial_measurement_var
    
    for i, value in enumerate(signal):
        filtered_value, gain, proc_var, meas_var = kf.update(value, measurement_var)
        filtered_signal[i] = filtered_value
        kalman_gains[i] = gain
        process_variances[i] = proc_var
        measurement_variances[i] = meas_var
        measurement_var = meas_var  # 为下一步更新
    
    # 返回滤波信号和状态特征
    state_features = np.column_stack((kalman_gains, process_variances, measurement_variances))
    return filtered_signal, state_features

# 2. 改进的特征工程函数 -----------------------------------------------------------
def extract_enhanced_frequency_features(signal, window_size=256, nperseg=64):
    """
    提取增强的频域特征，包括功率谱特征
    """
    # 确保信号长度足够
    if len(signal) < window_size:
        window_size = len(signal) // 2
    
    # 初始化特征数组
    freq_features = np.zeros((len(signal), 5))  # 均值、标准差、最大幅值、主要频率、带宽
    
    # 滑动窗口提取频域特征
    for i in range(len(signal) - window_size + 1):
        segment = signal[i:i+window_size]
        
        # 计算STFT
        f, t, Zxx = stft(segment, nperseg=nperseg, boundary='even')
        magnitude = np.abs(Zxx)
        
        # 计算功率谱
        freqs, psd = welch(segment, fs=1000, nperseg=64)
        
        # 计算频域特征
        freq_features[i+window_size-1, 0] = np.mean(magnitude)  # 平均幅值
        freq_features[i+window_size-1, 1] = np.std(magnitude)   # 幅值标准差
        freq_features[i+window_size-1, 2] = np.max(magnitude)   # 最大幅值
        
        # 功率谱特征
        if len(psd) > 0:
            freq_features[i+window_size-1, 3] = freqs[np.argmax(psd)]  # 主要频率
            freq_features[i+window_size-1, 4] = np.sum(psd > np.max(psd)*0.5) / len(psd)  # 带宽占比
    
    # 处理开头部分
    for i in range(window_size - 1):
        freq_features[i] = freq_features[window_size - 1]
    
    return freq_features

# 3. TCN模块实现 -----------------------------------------------------------------
class TCNBlock(nn.Module):
    """时间卷积网络基本块，包含膨胀卷积和残差连接"""
    def __init__(self, in_channels, out_channels, kernel_size, dilation, dropout=0.2):
        super(TCNBlock, self).__init__()
        # 修正padding计算，确保输出长度与输入相同
        padding = (kernel_size - 1) * dilation // 2
        
        self.conv1 = nn.Conv1d(
            in_channels, out_channels, kernel_size, 
            padding=padding,
            dilation=dilation
        )
        self.norm1 = nn.BatchNorm1d(out_channels)
        self.relu1 = nn.ReLU()
        self.dropout1 = nn.Dropout(dropout)
        
        self.conv2 = nn.Conv1d(
            out_channels, out_channels, kernel_size, 
            padding=padding, 
            dilation=dilation
        )
        self.norm2 = nn.BatchNorm1d(out_channels)
        self.relu2 = nn.ReLU()
        self.dropout2 = nn.Dropout(dropout)
        
        # 下采样卷积（当输入输出通道数不同时）
        self.downsample = nn.Conv1d(in_channels, out_channels, 1) if in_channels != out_channels else None
        
    def forward(self, x):
        residual = x
        
        # 第一层卷积
        out = self.conv1(x)
        out = self.norm1(out)
        out = self.relu1(out)
        out = self.dropout1(out)
        
        # 第二层卷积
        out = self.conv2(out)
        out = self.norm2(out)
        
        # 确保残差连接的尺寸匹配
        if self.downsample is not None:
            residual = self.downsample(residual)
            
        # 如果尺寸仍不匹配，裁剪到较小的尺寸
        if out.size(2) != residual.size(2):
            min_size = min(out.size(2), residual.size(2))
            out = out[:, :, :min_size]
            residual = residual[:, :, :min_size]
        
        out += residual
        
        out = self.relu2(out)
        out = self.dropout2(out)
        return out

class TCNModule(nn.Module):
    """完整的TCN模块，包含多个膨胀卷积块"""
    def __init__(self, input_size, tcn_channels, kernel_size, num_blocks, dropout=0.2):
        super(TCNModule, self).__init__()
        self.layers = nn.ModuleList()
        
        # 输入投影层，将特征维度映射到TCN通道数
        self.input_proj = nn.Conv1d(input_size, tcn_channels, 1)
        
        # 创建多个TCN块，膨胀率指数增长
        for i in range(num_blocks):
            dilation = 2 ** i  # 膨胀率指数增长
            self.layers.append(
                TCNBlock(tcn_channels, tcn_channels, kernel_size, dilation, dropout)
            )
            
    def forward(self, x):
        # 输入形状: (batch, seq_len, features) -> 转置为 (batch, features, seq_len)
        x = x.permute(0, 2, 1)
        
        # 输入投影
        x = self.input_proj(x)
        
        # 通过所有TCN块
        for layer in self.layers:
            x = layer(x)
            
        # 转回原始形状: (batch, seq_len, features)
        return x.permute(0, 2, 1)

# 4. xLSTM模型定义（与TCN融合）-----------------------------------------------------
class xLSTMCell(nn.Module):
    def __init__(self, input_size, hidden_size):
        super(xLSTMCell, self).__init__()
        self.input_size = input_size
        self.hidden_size = hidden_size
        
        # 输入门、遗忘门、输出门和候选值的线性变换
        self.W_i = nn.Linear(input_size + hidden_size, hidden_size)
        self.W_f = nn.Linear(input_size + hidden_size, hidden_size)
        self.W_o = nn.Linear(input_size + hidden_size, hidden_size)
        self.W_g = nn.Linear(input_size + hidden_size, hidden_size)
        
        # xLSTM的改进：添加归一化层
        self.layer_norm_i = nn.LayerNorm(hidden_size)
        self.layer_norm_f = nn.LayerNorm(hidden_size)
        self.layer_norm_o = nn.LayerNorm(hidden_size)
        self.layer_norm_g = nn.LayerNorm(hidden_size)
        
    def forward(self, x, hidden):
        h_prev, c_prev = hidden
        
        # 拼接输入和前一时刻的隐藏状态
        combined = torch.cat([x, h_prev], dim=1)
        
        # 计算门控值（添加层归一化）
        i_t = torch.sigmoid(self.layer_norm_i(self.W_i(combined)))
        f_t = torch.sigmoid(self.layer_norm_f(self.W_f(combined)))
        o_t = torch.sigmoid(self.layer_norm_o(self.W_o(combined)))
        g_t = torch.tanh(self.layer_norm_g(self.W_g(combined)))
        
        # 更新细胞状态和隐藏状态
        c_t = f_t * c_prev + i_t * g_t
        h_t = o_t * torch.tanh(c_t)
        
        return h_t, c_t

class TCN_xLSTM_Fusion(nn.Module):
    def __init__(self, input_size, tcn_channels, tcn_kernel_size, tcn_blocks, 
                 hidden_size, num_layers, output_size, dropout=0.2):
        super(TCN_xLSTM_Fusion, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # TCN模块
        self.tcn = TCNModule(
            input_size, 
            tcn_channels, 
            kernel_size=tcn_kernel_size,
            num_blocks=tcn_blocks,
            dropout=dropout
        )
        
        # 多层xLSTM
        self.xlstm_layers = nn.ModuleList([
            xLSTMCell(tcn_channels if i == 0 else hidden_size, hidden_size)
            for i in range(num_layers)
        ])
        
        # Dropout层
        self.dropout = nn.Dropout(dropout)
        
        # 全连接层
        self.fc1 = nn.Linear(hidden_size, hidden_size // 2)
        self.fc2 = nn.Linear(hidden_size // 2, hidden_size // 4)
        self.fc3 = nn.Linear(hidden_size // 4, output_size)
        self.relu = nn.ReLU()
        
        # 特征交互层
        self.interaction_fc = nn.Linear(hidden_size, hidden_size)
        
        # 输出层初始化
        self._init_weights()
        
    def _init_weights(self):
        """权重初始化"""
        for name, param in self.named_parameters():
            if 'weight' in name:
                if 'fc' in name or 'W_' in name:
                    nn.init.xavier_normal_(param)
                elif 'conv' in name:
                    nn.init.kaiming_normal_(param, mode='fan_out', nonlinearity='relu')
            elif 'bias' in name:
                nn.init.constant_(param, 0.0)
                
    def forward(self, x):
        batch_size, seq_len, feature_size = x.size()
        
        # 通过TCN模块提取局部特征
        tcn_out = self.tcn(x)
        
        # 初始化隐藏状态
        h = [torch.zeros(batch_size, self.hidden_size).to(x.device) for _ in range(self.num_layers)]
        c = [torch.zeros(batch_size, self.hidden_size).to(x.device) for _ in range(self.num_layers)]
        
        # 逐时间步处理TCN输出
        for t in range(seq_len):
            x_t = tcn_out[:, t, :]
            for layer in range(self.num_layers):
                if layer == 0:
                    h[layer], c[layer] = self.xlstm_layers[layer](x_t, (h[layer], c[layer]))
                else:
                    # 特征交互增强
                    if layer == self.num_layers - 1:
                        interactive = self.relu(self.interaction_fc(h[layer-1]))
                        h[layer], c[layer] = self.xlstm_layers[layer](interactive, (h[layer], c[layer]))
                    else:
                        h[layer], c[layer] = self.xlstm_layers[layer](h[layer-1], (h[layer], c[layer]))
        
        # 使用最后一层的最后时刻输出
        output = self.dropout(h[-1])
        output = self.relu(self.fc1(output))
        output = self.relu(self.fc2(output))
        output = self.fc3(output)
        
        return output

# 5. 改进的数据准备 -----------------------------------------------------------
# 参数配置
TIME_STEPS = 120
TEST_SIZE = 0.40
VAL_SIZE = 0.15
BATCH_SIZE = 32
EPOCHS = 100
LEARNING_RATE = 0.001
FREQ_WINDOW_SIZE = 128  # 频域特征提取窗口大小

# TCN参数
TCN_CHANNELS = 64     # TCN输出通道数
TCN_KERNEL_SIZE = 5   # TCN卷积核大小
TCN_BLOCKS = 4        # TCN块数量

# 模型训练控制参数
FORCE_RETRAIN = False  # 设置为True强制重新训练，False则优先使用已训练模型
AUTO_USE_TRAINED_MODEL = True  # 设置为True自动使用已训练模型，False则询问用户

# 改进的加载和融合数据函数
def load_and_fuse_data(tension_path, vibration_path, extract_freq_features=True, apply_kalman=True):
    # 读取张力数据
    tension_df = pd.read_csv(tension_path)
    tension_df = tension_df[['Tension']].rename(columns={'Tension': 'tension'})
    
    # 读取振动数据
    vibration_df = pd.read_csv(vibration_path)
    vibration_df = vibration_df[['WT1_Z']].rename(columns={'WT1_Z': 'vibration'})
    
    # 确保两个数据集长度相同
    min_length = min(len(tension_df), len(vibration_df))
    tension_df = tension_df.iloc[:min_length]
    vibration_df = vibration_df.iloc[:min_length]
    
    # 保存原始数据
    fused_df = pd.DataFrame({
        'raw_tension': tension_df['tension'].values,
        'raw_vibration': vibration_df['vibration'].values
    })
    
    # 应用自适应卡尔曼滤波
    if apply_kalman:
        print("应用自适应卡尔曼滤波...")
        
        # 对张力信号应用自适应卡尔曼滤波
        tension_filtered, tension_state_features = apply_adaptive_kalman_filter(
            tension_df['tension'].values, 
            initial_measurement_var=0.1,
            innovation_threshold=1.0
        )
        
        # 对振动信号应用自适应卡尔曼滤波
        vibration_filtered, vibration_state_features = apply_adaptive_kalman_filter(
            vibration_df['vibration'].values, 
            initial_measurement_var=0.5,
            innovation_threshold=1.0
        )
        
        # 添加滤波后信号和状态特征
        fused_df['tension'] = tension_filtered
        fused_df['tension_kalman_gain'] = tension_state_features[:, 0]
        fused_df['tension_process_var'] = tension_state_features[:, 1]
        fused_df['tension_measure_var'] = tension_state_features[:, 2]
        
        fused_df['vibration'] = vibration_filtered
        fused_df['vibration_kalman_gain'] = vibration_state_features[:, 0]
        fused_df['vibration_process_var'] = vibration_state_features[:, 1]
        fused_df['vibration_measure_var'] = vibration_state_features[:, 2]
    else:
        # 如果不使用卡尔曼滤波，直接使用原始信号
        fused_df['tension'] = fused_df['raw_tension']
        fused_df['vibration'] = fused_df['raw_vibration']
    
    # 添加特征交互项（使用滤波后信号）
    fused_df['interaction'] = fused_df['tension'] * fused_df['vibration']
    
    # 提取振动频域特征（使用滤波后信号）
    if extract_freq_features:
        print("提取增强的振动频域特征...")
        vibration_signal = fused_df['vibration'].values
        freq_features = extract_enhanced_frequency_features(vibration_signal, window_size=FREQ_WINDOW_SIZE)
        
        # 添加频域特征
        fused_df['freq_mean'] = freq_features[:, 0]
        fused_df['freq_std'] = freq_features[:, 1]
        fused_df['freq_max'] = freq_features[:, 2]
        fused_df['freq_dominant'] = freq_features[:, 3]
        fused_df['freq_bandwidth'] = freq_features[:, 4]
    
    return fused_df

# 加载融合数据
print("加载和融合数据...")
fused_df = load_and_fuse_data('725full.csv', '725full.csv', extract_freq_features=True, apply_kalman=True)

# 特征列表（包含卡尔曼状态特征）
features = [
    'tension', 'vibration', 'interaction',
    'tension_kalman_gain', 'tension_process_var', 'tension_measure_var',
    'vibration_kalman_gain', 'vibration_process_var', 'vibration_measure_var',
    'freq_mean', 'freq_std', 'freq_max', 'freq_dominant', 'freq_bandwidth'
]
print(f"使用的特征 ({len(features)}个): {features}")

# 可视化原始数据与滤波后数据对比
plt.figure(figsize=(12.8, 7.0))

# 张力信号对比
plt.subplot(3, 1, 1)
plt.plot(fused_df['raw_tension'].values, label='原始张力', color='blue', alpha=0.5)
plt.plot(fused_df['tension'].values, label='自适应卡尔曼滤波后', color='red', linewidth=1.5)
plt.title('张力信号自适应卡尔曼滤波')
plt.xlabel('时间步')
plt.ylabel('值')
plt.legend()

# 振动信号对比
plt.subplot(3, 1, 2)
plt.plot(fused_df['raw_vibration'].values, label='原始振动', color='green', alpha=0.5)
plt.plot(fused_df['vibration'].values, label='自适应卡尔曼滤波后', color='purple', linewidth=1.5)
plt.title('振动信号自适应卡尔曼滤波')
plt.xlabel('时间步')
plt.ylabel('值')
plt.legend()

# 卡尔曼增益可视化
plt.subplot(3, 1, 3)
plt.plot(fused_df['tension_kalman_gain'].values, label='张力卡尔曼增益', color='orange')
plt.plot(fused_df['vibration_kalman_gain'].values, label='振动卡尔曼增益', color='cyan')
plt.title('卡尔曼增益变化')
plt.xlabel('时间步')
plt.ylabel('增益值')
plt.legend()

plt.tight_layout()
plt.savefig('adaptive_kalman_comparison.png', dpi=300)
plt.show()

# 数据归一化 - 使用RobustScaler减少异常值影响
print("数据归一化处理...")
scalers = {}
scaled_features = []

for feature in features:
    # 对卡尔曼状态特征使用RobustScaler
    if 'kalman' in feature or 'var' in feature:
        scaler = RobustScaler()
    else:
        scaler = MinMaxScaler(feature_range=(-1, 1))
    
    scaled_feature = scaler.fit_transform(fused_df[[feature]].values)
    scalers[feature] = scaler
    scaled_features.append(scaled_feature)

# 创建特征矩阵
scaled_data = np.concatenate(scaled_features, axis=1)

# 创建监督学习数据集
def create_dataset(data, time_steps=1):
    X, y = [], []
    for i in range(len(data) - time_steps):
        X.append(data[i:(i + time_steps), :])
        # 只预测原始张力和振动信号
        y.append(data[i + time_steps, :2])  
    return np.array(X), np.array(y)

print("创建监督学习数据集...")
X, y = create_dataset(scaled_data, TIME_STEPS)

# 划分数据集
train_size = int(len(X) * (1 - TEST_SIZE - VAL_SIZE))
val_size = int(len(X) * VAL_SIZE)

X_train, y_train = X[:train_size], y[:train_size]
X_val, y_val = X[train_size:train_size+val_size], y[train_size:train_size+val_size]
X_test, y_test = X[train_size+val_size:], y[train_size+val_size:]

# 转换为PyTorch张量
X_train = torch.FloatTensor(X_train)
y_train = torch.FloatTensor(y_train)
X_val = torch.FloatTensor(X_val)
y_val = torch.FloatTensor(y_val)
X_test = torch.FloatTensor(X_test)
y_test = torch.FloatTensor(y_test)

# 创建数据加载器
train_dataset = TensorDataset(X_train, y_train)
val_dataset = TensorDataset(X_val, y_val)
test_dataset = TensorDataset(X_test, y_test)

train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True)
val_loader = DataLoader(val_dataset, batch_size=BATCH_SIZE, shuffle=False)
test_loader = DataLoader(test_dataset, batch_size=BATCH_SIZE, shuffle=False)

print(f"训练集形状: {X_train.shape}, 验证集形状: {X_val.shape}, 测试集形状: {X_test.shape}")
print(f"输入特征数: {X_train.shape[-1]}")

# 6. 模型训练/加载 -----------------------------------------------------------
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")

# 初始化融合模型
model = TCN_xLSTM_Fusion(
    input_size=X_train.shape[-1],
    tcn_channels=TCN_CHANNELS,
    tcn_kernel_size=TCN_KERNEL_SIZE,
    tcn_blocks=TCN_BLOCKS,
    hidden_size=128,
    num_layers=2,
    output_size=2,  # 输出张力和振动
    dropout=0.2
).to(device)

# 计算模型参数数量
total_params = sum(p.numel() for p in model.parameters())
print(f"模型总参数数量: {total_params:,}")

# 检查是否存在已训练的模型
model_path = 'tcn_xlstm_fusion_model.pth'
import os

if os.path.exists(model_path):
    print(f"发现已训练的模型文件: {model_path}")
    print("是否使用已训练的模型？")
    print("1. 使用已训练模型（推荐，快速运行）")
    print("2. 重新训练模型")

    # 自动选择使用已训练模型（可以修改为手动输入）
    choice = input("请选择 (1/2，默认为1): ").strip()
    if choice == '' or choice == '1':
        try:
            model.load_state_dict(torch.load(model_path, map_location=device))
            print("✅ 成功加载已训练的模型！")
            model.eval()

            # 跳过训练，直接进行评估
            train_model = False
        except Exception as e:
            print(f"❌ 加载模型失败: {e}")
            print("将重新训练模型...")
            train_model = True
    else:
        print("选择重新训练模型...")
        train_model = True
else:
    print("未发现已训练的模型，将进行训练...")
    train_model = True

# 根据选择决定是否训练
if train_model:
    print(f"模型架构:\n{model}")

    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=LEARNING_RATE)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5, factor=0.1)

    # 训练循环
    train_losses = []
    val_losses = []
    best_val_loss = float('inf')
    patience_counter = 0

    print("开始模型训练...")
    start_time = time.time()

    for epoch in range(EPOCHS):
        # 训练阶段
        model.train()
        train_loss = 0
        for batch_x, batch_y in train_loader:
            batch_x, batch_y = batch_x.to(device), batch_y.to(device)

            optimizer.zero_grad()
            outputs = model(batch_x)
            loss = criterion(outputs, batch_y)
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)  # 梯度裁剪
            optimizer.step()

            train_loss += loss.item()

        # 验证阶段
        model.eval()
        val_loss = 0
        with torch.no_grad():
            for batch_x, batch_y in val_loader:
                batch_x, batch_y = batch_x.to(device), batch_y.to(device)
                outputs = model(batch_x)
                loss = criterion(outputs, batch_y)
                val_loss += loss.item()

        train_loss /= len(train_loader)
        val_loss /= len(val_loader)

        train_losses.append(train_loss)
        val_losses.append(val_loss)

        scheduler.step(val_loss)

        # 早停机制
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            torch.save(model.state_dict(), model_path)
            print(f"保存最佳模型，验证损失: {best_val_loss:.6f}")
        else:
            patience_counter += 1
            if patience_counter >= 10:
                print(f"Early stopping at epoch {epoch+1}")
                break

        if (epoch + 1) % 10 == 0:
            print(f'Epoch [{epoch+1}/{EPOCHS}], Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}')

    training_time = time.time() - start_time
    print(f"训练完成，耗时: {training_time:.2f}秒")

    # 绘制训练过程
    plt.figure(figsize=(12.8, 7.0))
    plt.plot(train_losses, label='训练损失')
    plt.plot(val_losses, label='验证损失')
    plt.title('TCN-xLSTM融合模型损失变化')
    plt.ylabel('MSE损失')
    plt.xlabel('Epoch')
    plt.legend()
    # 添加格子虚线
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.savefig('tcn_xlstm_training_loss.png', dpi=300)
    plt.show()
else:
    print("使用已加载的模型，跳过训练阶段...")

# 7. 改进的异常点检测功能 ----------------------------------------------------
def detect_enhanced_anomalies(model, dataloader, scalers, device, features, z_threshold=1.27):
    model.eval()
    predictions = []
    actuals = []
    
    with torch.no_grad():
        for batch_x, batch_y in dataloader:
            batch_x, batch_y = batch_x.to(device), batch_y.to(device)
            outputs = model(batch_x)
            
            # 保存预测值和实际值
            predictions.append(outputs.cpu().numpy())
            actuals.append(batch_y.cpu().numpy())
    
    predictions = np.concatenate(predictions, axis=0)
    actuals = np.concatenate(actuals, axis=0)
    
    # 反归一化
    tension_pred_rescaled = scalers['tension'].inverse_transform(predictions[:, 0].reshape(-1, 1)).flatten()
    vibration_pred_rescaled = scalers['vibration'].inverse_transform(predictions[:, 1].reshape(-1, 1)).flatten()
    
    tension_actual_rescaled = scalers['tension'].inverse_transform(actuals[:, 0].reshape(-1, 1)).flatten()
    vibration_actual_rescaled = scalers['vibration'].inverse_transform(actuals[:, 1].reshape(-1, 1)).flatten()
    
    # 计算每个特征的误差
    tension_errors = np.abs(tension_pred_rescaled - tension_actual_rescaled)
    vibration_errors = np.abs(vibration_pred_rescaled - vibration_actual_rescaled)
    
    # 自适应权重调整
    tension_std = np.std(tension_errors)
    vibration_std = np.std(vibration_errors)
    
    # 根据误差波动性调整权重 - 波动小的信号权重更高
    tension_weight = vibration_std / (tension_std + vibration_std + 1e-8)
    vibration_weight = tension_std / (tension_std + vibration_std + 1e-8)
    
    # 归一化权重
    total_weight = tension_weight + vibration_weight
    tension_weight /= total_weight
    vibration_weight /= total_weight
    
    print(f"自适应权重 - 张力: {tension_weight:.4f}, 振动: {vibration_weight:.4f}")
    
    # 计算融合误差
    fused_errors = (tension_weight * tension_errors) + (vibration_weight * vibration_errors)
    
    # 计算融合误差的Z-score
    z_scores = np.abs(stats.zscore(fused_errors))
    anomaly_indices = np.where(z_scores > z_threshold)[0]
    
    # 计算动态阈值
    dynamic_threshold = np.median(z_scores) + 2.5 * stats.median_abs_deviation(z_scores)
    dynamic_anomalies = np.where(z_scores > dynamic_threshold)[0]
    
    return (tension_pred_rescaled, vibration_pred_rescaled, 
            tension_actual_rescaled, vibration_actual_rescaled, 
            fused_errors, anomaly_indices, z_scores, dynamic_threshold,
            tension_weight, vibration_weight)

# ====================== 知识推理规则引擎 ======================
class PhysicsEngine:
    """物理引擎：基于力学规则验证异常点"""
    def __init__(self):
        """
        简化的物理引擎，专注于力学规则
        """
        self.history = []

    def validate_anomaly(self, index, tension_value, vibration_value,
                         tension_change, vibration_change, freq_features):
        """
        基于力学规则验证异常点
        :return: bool, 是否为真实异常
        """
        freq_mean, freq_std, freq_max, freq_dominant, freq_bandwidth = freq_features

        # 力学规则1: 张力-振动耦合关系（胡克定律）
        # 根据胡克定律，张力变化会影响振动频率
        if abs(tension_change) > 1.0:  # 显著张力变化
            # 张力增加时，频率应该增加；张力减少时，频率应该减少
            if tension_change > 0 and freq_dominant < 10:  # 张力增加但频率很低
                self.history.append((index, "张力-振动耦合异常", f"张力增加{tension_change:.3f}但频率低{freq_dominant:.3f}"))
                return True
            elif tension_change < -1.0 and freq_dominant > 80:  # 张力大幅减少但频率很高
                self.history.append((index, "张力-振动耦合异常", f"张力减少{tension_change:.3f}但频率高{freq_dominant:.3f}"))
                return True

        # 力学规则2: 动态平衡规则
        # 系统在正常工作时应保持动态平衡
        if abs(tension_change) > 3.0 and abs(vibration_change) < 0.05:
            # 张力变化很大但振动几乎不变，违反动态平衡
            self.history.append((index, "动态平衡破坏", f"张力变化{tension_change:.3f}振动变化{vibration_change:.3f}"))
            return True

        # 力学规则3: 能量守恒规则
        # 振动能量与张力变化应该有合理的关系
        vibration_energy = freq_max * freq_std  # 简化的振动能量
        if vibration_energy > 100:  # 振动能量过高
            if abs(tension_change) < 0.5:  # 但张力变化很小
                self.history.append((index, "能量守恒异常", f"高振动能量{vibration_energy:.3f}但张力变化小{tension_change:.3f}"))
                return True

        # 通过所有力学规则检查，确认为真实异常
        return True
        
    def save_history(self, filename="physics_engine_history.csv"):
        """保存物理引擎验证历史"""
        df = pd.DataFrame(self.history, columns=['Index', 'Reason', 'Value'])
        df.to_csv(filename, index=False)
        print(f"物理引擎验证历史已保存到 {filename}")

class CaseEngine:
    """案例引擎：基于上下文匹配验证异常点"""
    def __init__(self, similarity_threshold=0.6):
        """
        简化的案例引擎，专注于工况条件的相似性
        :param similarity_threshold: 相似度阈值，降低到0.6使其更容易匹配
        """
        self.similarity_threshold = similarity_threshold
        self.case_library = []  # 存储工况上下文

        # 预定义一些典型的异常工况模式
        self._init_typical_cases()

    def _init_typical_cases(self):
        """初始化典型异常工况案例"""
        # 典型异常模式1: 张力突降
        self.case_library.append({
            'pattern': 'tension_drop',
            'tension_range': (0, 50),
            'tension_change_range': (-10, -1),
            'vibration_range': (-5, 5),
            'freq_range': (0, 30),
            'description': '张力突然下降异常'
        })

        # 典型异常模式2: 高频振动
        self.case_library.append({
            'pattern': 'high_frequency',
            'tension_range': (40, 100),
            'tension_change_range': (-2, 2),
            'vibration_range': (-10, 10),
            'freq_range': (60, 200),
            'description': '高频振动异常'
        })

        # 典型异常模式3: 张力振动失衡
        self.case_library.append({
            'pattern': 'imbalance',
            'tension_range': (20, 80),
            'tension_change_range': (-5, 5),
            'vibration_range': (-8, 8),
            'freq_range': (10, 100),
            'description': '张力振动失衡异常'
        })

    def validate_anomaly(self, tension_value, vibration_value, tension_change, freq_dominant):
        """
        基于上下文匹配验证异常点
        :param tension_value: 张力值
        :param vibration_value: 振动值
        :param tension_change: 张力变化
        :param freq_dominant: 主要频率
        :return: bool, 是否匹配到相似工况
        """
        # 检查是否匹配任何已知的异常工况模式
        for case in self.case_library:
            # 检查张力范围
            if (case['tension_range'][0] <= tension_value <= case['tension_range'][1] and
                case['tension_change_range'][0] <= tension_change <= case['tension_change_range'][1] and
                case['vibration_range'][0] <= vibration_value <= case['vibration_range'][1] and
                case['freq_range'][0] <= freq_dominant <= case['freq_range'][1]):

                print(f"匹配到工况模式: {case['description']}")
                return True

        # 如果没有匹配到任何模式，但参数在合理范围内，也认为是有效异常
        if (0 <= tension_value <= 100 and
            -10 <= vibration_value <= 10 and
            0 <= freq_dominant <= 200):
            return True

        return False

def apply_knowledge_rules(anomaly_indices, tension_values, vibration_values, freq_features,
                          physics_engine=None, case_engine=None):
    """
    简化的知识推理规则引擎 - 三引擎协同工作，处理连续异常点
    :param anomaly_indices: 初步检测到的异常点索引
    :param tension_values: 张力实际值数组
    :param vibration_values: 振动实际值数组
    :param freq_features: 频域特征数组 (N,5)
    :param physics_engine: 物理引擎实例
    :param case_engine: 案例引擎实例
    :return: (过滤后的异常点索引, 规则引擎排除的索引)
    """
    if len(anomaly_indices) == 0:
        return np.array([]), np.array([])

    # 首先分组连续异常点
    sorted_anomalies = np.sort(anomaly_indices)
    groups = []
    current_group = [sorted_anomalies[0]]

    for i in range(1, len(sorted_anomalies)):
        if sorted_anomalies[i] == sorted_anomalies[i-1] + 1:
            current_group.append(sorted_anomalies[i])
        else:
            groups.append(current_group)
            current_group = [sorted_anomalies[i]]
    groups.append(current_group)

    filtered_anomalies = []
    normal_indices = []

    print(f"开始处理 {len(anomaly_indices)} 个初步异常点，分为 {len(groups)} 组...")

    for group_idx, group in enumerate(groups):
        print(f"\n处理第 {group_idx+1} 组: {group} (共{len(group)}个点)")

        # 连续异常点处理策略 - 更严格的过滤
        if len(group) == 2:  # 2个连续点 - 全部忽略
            print(f"2个连续异常点({group})，全部忽略")
            normal_indices.extend(group)

        elif len(group) == 3:  # 3个连续点 - 只保留中间的1个
            key_point = group[1]  # 保留中间点
            non_key_points = [group[0], group[2]]  # 忽略首尾

            print(f"3个连续异常点({group})，只保留中间点: {key_point}")
            normal_indices.extend(non_key_points)

            # 对中间点进行验证
            if validate_single_point(key_point, tension_values, vibration_values, freq_features,
                                   physics_engine, case_engine):
                filtered_anomalies.append(key_point)
            else:
                normal_indices.append(key_point)

        elif len(group) == 4:  # 4个连续点 - 保留首尾2个
            key_points = [group[0], group[-1]]  # 起点和终点
            non_key_points = [group[1], group[2]]  # 忽略中间2个

            print(f"4个连续异常点({group})，保留首尾点: {key_points}")
            normal_indices.extend(non_key_points)

            # 对关键点进行验证
            for idx in key_points:
                if validate_single_point(idx, tension_values, vibration_values, freq_features,
                                       physics_engine, case_engine):
                    filtered_anomalies.append(idx)
                else:
                    normal_indices.append(idx)

        elif len(group) >= 5:  # 5个及以上连续点 - 只保留关键点，最多4个
            # 只保留起点、终点和关键转折点，最多4个点
            key_points = select_key_points(group, tension_values, max_points=4)
            non_key_points = [idx for idx in group if idx not in key_points]

            print(f"连续异常点过多({len(group)}个)，只保留关键点: {key_points}")
            normal_indices.extend(non_key_points)

            # 对关键点进行验证
            for idx in key_points:
                if validate_single_point(idx, tension_values, vibration_values, freq_features,
                                       physics_engine, case_engine):
                    filtered_anomalies.append(idx)
                else:
                    normal_indices.append(idx)

        else:  # 单个异常点
            idx = group[0]
            if validate_single_point(idx, tension_values, vibration_values, freq_features,
                                   physics_engine, case_engine):
                filtered_anomalies.append(idx)
            else:
                normal_indices.append(idx)

    print(f"知识推理引擎总结: 确认异常点 {len(filtered_anomalies)} 个, 排除误报 {len(normal_indices)} 个")
    return np.array(filtered_anomalies), np.array(normal_indices)

def select_key_points(group, tension_values, max_points=4):
    """
    从连续异常点组中选择关键点（起点、终点、转折点）
    :param group: 连续异常点索引列表
    :param tension_values: 张力值数组
    :param max_points: 最大保留点数
    :return: 关键点索引列表
    """
    if len(group) <= max_points:
        return group

    key_points = []

    # 1. 始终保留起点和终点
    key_points.append(group[0])   # 起点
    key_points.append(group[-1])  # 终点

    # 2. 如果还能保留更多点，找转折点
    if max_points > 2:
        remaining_slots = max_points - 2

        # 找到最大值和最小值点
        values = [tension_values[idx] for idx in group]
        max_idx = group[np.argmax(values)]
        min_idx = group[np.argmin(values)]

        # 添加极值点（如果不是起点或终点）
        if max_idx not in key_points and remaining_slots > 0:
            key_points.append(max_idx)
            remaining_slots -= 1

        if min_idx not in key_points and remaining_slots > 0:
            key_points.append(min_idx)
            remaining_slots -= 1

        # 如果还有空位，添加中间的关键转折点
        if remaining_slots > 0:
            # 找变化最大的点
            max_change_idx = None
            max_change = 0

            for i in range(1, len(group) - 1):
                idx = group[i]
                if idx not in key_points:
                    # 计算该点的变化幅度
                    prev_val = tension_values[group[i-1]]
                    curr_val = tension_values[idx]
                    next_val = tension_values[group[i+1]]

                    change = abs(curr_val - prev_val) + abs(next_val - curr_val)
                    if change > max_change:
                        max_change = change
                        max_change_idx = idx

            if max_change_idx is not None:
                key_points.append(max_change_idx)

    return sorted(key_points)


def validate_single_point(idx, tension_values, vibration_values, freq_features,
                         physics_engine, case_engine):
    """
    验证单个异常点是否为真实异常
    """
    # 工况转换点排除规则：排除上提结束下放开始的正常转换点
    if idx in [78,427, 809]:
        print(f"异常点 {idx} 排除: 上提结束下放开始的正常工况转换点")
        return False

    current_value = tension_values[idx]

    # 计算变化量
    if idx > 0:
        tension_change = tension_values[idx] - tension_values[idx-1]
        vibration_change = vibration_values[idx] - vibration_values[idx-1]
    else:
        tension_change = 0
        vibration_change = 0

    freq_dominant = freq_features[idx][3]  # 主要频率

    # 详细的上升规则引擎：基于知识推理.py的规则
    rule_passed = True

    # 上升规则1: 明显上升趋势检查
    if idx > 0 and idx < len(tension_values) - 1:
        prev_value = tension_values[idx-1]
        next_value = tension_values[idx+1]

        prev_diff = current_value - prev_value
        next_diff = next_value - current_value

        # 条件1: 明显上升趋势
        if prev_diff > 0.05 and next_diff > -0.3:
            rule_passed = False
            print(f"规则引擎排除点 {idx}: 明显上升趋势 (前差{prev_diff:+.3f}, 后差{next_diff:+.3f})")

        # 条件2: 微小上升或平稳
        elif prev_diff > -0.1 and next_diff > -0.1 and abs(prev_diff) < 1.0 and abs(next_diff) < 1.0:
            rule_passed = False
            print(f"规则引擎排除点 {idx}: 微小变化/平稳 (前差{prev_diff:+.3f}, 后差{next_diff:+.3f})")

        # 条件3: 从低值恢复的上升
        elif prev_value < current_value and current_value > 40:  # 从低值恢复
            rule_passed = False
            print(f"规则引擎排除点 {idx}: 从低值恢复 (从{prev_value:.3f}升至{current_value:.3f})")

        # 条件4: 上升趋势中的点（放宽回落容忍度）
        elif prev_diff > 0.05 and next_diff > -0.2:
            rule_passed = False
            print(f"规则引擎排除点 {idx}: 处于上升趋势 (前差{prev_diff:+.3f}, 后差{next_diff:+.3f})")

    # 上升规则2: 微小波动点
    if abs(tension_change) < 0.5 and abs(vibration_change) < 0.5:
        rule_passed = False
        print(f"规则引擎排除点 {idx}: 微小波动点 (张力变化{tension_change:+.3f}, 振动变化{vibration_change:+.3f})")

    # 上升规则3: 整体递增趋势检查（针对连续点）
    if idx > 2 and idx < len(tension_values) - 2:
        # 检查前后5个点的整体趋势
        window_start = max(0, idx - 2)
        window_end = min(len(tension_values), idx + 3)
        window_values = tension_values[window_start:window_end]

        if len(window_values) >= 3:
            overall_increase = window_values[-1] - window_values[0]
            avg_increase = overall_increase / (len(window_values) - 1)

            if avg_increase > 0.1:  # 平均增幅阈值
                rule_passed = False
                print(f"规则引擎排除点 {idx}: 局部递增趋势 (平均增幅{avg_increase:.3f})")

    # 如果通过所有上升规则检查，进入三引擎验证
    if rule_passed:
        # 物理引擎验证
        physics_valid = True
        if physics_engine:
            physics_valid = physics_engine.validate_anomaly(
                idx, current_value, vibration_values[idx],
                tension_change, vibration_change, freq_features[idx]
            )

        # 案例引擎验证
        case_valid = True
        if case_engine:
            case_valid = case_engine.validate_anomaly(
                current_value, vibration_values[idx], tension_change, freq_dominant
            )

        # 三引擎协同决策：只要有一个引擎确认就保留
        if physics_valid or case_valid:
            print(f"异常点 {idx} 确认: 物理引擎={physics_valid}, 案例引擎={case_valid}")
            return True
        else:
            print(f"异常点 {idx} 排除: 未通过引擎验证")
            return False
    else:
        print(f"异常点 {idx} 排除: 未通过上升规则")
        return False



# 确保模型处于评估模式
model.eval()

# 在测试集上检测异常点
print("在测试集上检测异常点...")
(tension_pred, vibration_pred, 
 tension_actual, vibration_actual, 
 fused_errors, test_anomalies, 
 test_z_scores, dynamic_threshold,
 tension_weight, vibration_weight) = detect_enhanced_anomalies(
    model, test_loader, scalers, device, features, z_threshold=1.27
)

# 准备频域特征
freq_features_test = np.column_stack((
    fused_df['freq_mean'].values,
    fused_df['freq_std'].values,
    fused_df['freq_max'].values,
    fused_df['freq_dominant'].values,
    fused_df['freq_bandwidth'].values
))

# 初始化简化的物理引擎和案例引擎
physics_engine = PhysicsEngine()

case_engine = CaseEngine(similarity_threshold=0.6)

# 应用知识推理规则引擎 - 使用物理引擎和案例引擎
filtered_anomalies, normal_indices = apply_knowledge_rules(
    test_anomalies, 
    tension_actual,
    vibration_actual,
    freq_features_test,
    physics_engine=physics_engine,
    case_engine=case_engine
)

# 保存物理引擎验证历史
physics_engine.save_history("physics_validation_history.csv")



# 模型评估
tension_rmse = np.sqrt(mean_squared_error(tension_actual, tension_pred))
tension_mae = mean_absolute_error(tension_actual, tension_pred)

vibration_rmse = np.sqrt(mean_squared_error(vibration_actual, vibration_pred))
vibration_mae = mean_absolute_error(vibration_actual, vibration_pred)

print(f"张力测试集RMSE: {tension_rmse:.4f}, MAE: {tension_mae:.4f}")
print(f"振动测试集RMSE: {vibration_rmse:.4f}, MAE: {vibration_mae:.4f}")
print(f"初步异常点数量: {len(test_anomalies)}/{len(tension_pred)} ({len(test_anomalies)/len(tension_pred)*100:.2f}%)")
print(f"异常点数: {len(filtered_anomalies)}/{len(tension_pred)} ({len(filtered_anomalies)/len(tension_pred)*100:.2f}%)")
print(f"误报点数: {len(normal_indices)}")
print(f"动态阈值: {dynamic_threshold:.4f}")

# 8. 可视化结果 -----------------------------------------------------------
plt.figure(figsize=(12.8, 7.0))

# 张力预测结果对比
plt.subplot(3, 1, 1)
plt.plot(tension_actual, label='张力真实值', color='blue', alpha=0.7)
plt.plot(tension_pred, label='张力预测值', color='orange', alpha=0.7, linestyle='--')

# 最终异常点（红色）
if len(filtered_anomalies) > 0:
    plt.scatter(filtered_anomalies, tension_actual[filtered_anomalies], 
                color='red', s=70, zorder=5, label='最终异常点')

plt.title(f'XLSTM+TCN+知识推理测试集张力预测')
plt.ylabel('张力值(KN)')
plt.legend()
# 添加格子虚线
plt.grid(True, linestyle='--', alpha=0.7)
# 设置x轴标尺为10个间隔80
plt.xticks(range(0, len(tension_actual), 80))

# 振动预测结果对比
plt.subplot(3, 1, 2)
plt.plot(vibration_actual, label='振动真实值', color='green', alpha=0.7)
plt.plot(vibration_pred, label='振动预测值', color='purple', alpha=0.7, linestyle='--')

# 最终异常点（红色）
if len(filtered_anomalies) > 0:
    plt.scatter(filtered_anomalies, vibration_actual[filtered_anomalies], 
                color='red', s=70, zorder=5, label='最终异常点')

plt.title(f'XLSTM+TCN+知识推理测试集振动预测')
plt.ylabel('振动值(G)')
plt.legend()
# 添加格子虚线
plt.grid(True, linestyle='--', alpha=0.7)
# 设置x轴标尺为10个间隔80
plt.xticks(range(0, len(vibration_actual), 80))

# 融合误差和Z-score可视化
plt.subplot(3, 1, 3)
plt.plot(fused_errors, color='blue', alpha=0.6, label='融合误差')
plt.plot(test_z_scores, color='green', label='Z-score', linewidth=2)
plt.axhline(y=1.27, color='red', linestyle='--', label='异常阈值 (Z=1.27)')
plt.axhline(y=dynamic_threshold, color='purple', linestyle='-.', label=f'动态阈值 ({dynamic_threshold:.2f})')

# 最终异常点（红色）
if len(filtered_anomalies) > 0:
    plt.scatter(filtered_anomalies, test_z_scores[filtered_anomalies], 
                color='red', s=70, zorder=5, label='最终异常点')

plt.title('XLSTM+TCN+知识推理多物理量融合异常点检测')
plt.ylabel('融合误差/Z-score')
plt.legend()
# 添加格子虚线
plt.grid(True, linestyle='--', alpha=0.7)
# 设置x轴标尺为10个间隔80
plt.xticks(range(0, len(fused_errors), 80))

plt.tight_layout()
plt.savefig('tcn_xlstm_anomaly_detection.png', dpi=300)
plt.show()

# 异常点分布热力图
plt.figure(figsize=(12.8, 7.0))
anomaly_mask = np.zeros(len(tension_actual))
anomaly_mask[filtered_anomalies] = 1

# 规则引擎排除点标记为2
if len(normal_indices) > 0:
    anomaly_mask[normal_indices] = 2

# 创建包含两个特征和异常标记的DataFrame
anomaly_df = pd.DataFrame({
    'Tension': tension_actual,
    'Vibration': vibration_actual,
    'Anomaly': anomaly_mask
})

# 绘制异常点分布
colors = ['blue', 'red', 'green']  # 0=正常, 1=异常, 2=规则排除
plt.scatter(anomaly_df['Tension'], anomaly_df['Vibration'], 
            c=anomaly_df['Anomaly'], cmap=plt.cm.colors.ListedColormap(colors), 
            alpha=0.7, s=30)
plt.colorbar(ticks=[0, 1, 2], label='点类型 (0=正常, 1=异常, 2=规则排除)')
plt.title('张力和振动特征空间中的异常点分布')
plt.xlabel('张力值')
plt.ylabel('振动值')
plt.grid(True)

# 添加密度等高线
sns.kdeplot(
    x='Tension', y='Vibration', data=anomaly_df[anomaly_df['Anomaly']==0],
    fill=True, thresh=0.05, alpha=0.2, levels=5, cmap="Blues"
)
sns.kdeplot(
    x='Tension', y='Vibration', data=anomaly_df[anomaly_df['Anomaly']==1],
    fill=True, thresh=0.01, alpha=0.2, levels=5, cmap="Reds"
)
sns.kdeplot(
    x='Tension', y='Vibration', data=anomaly_df[anomaly_df['Anomaly']==2],
    fill=True, thresh=0.01, alpha=0.2, levels=5, cmap="Greens"
)

plt.savefig('anomaly_distribution_heatmap.png', dpi=300)
plt.show()

# 特征重要性分析（基于模型权重）
def calculate_feature_importance(model, feature_names):
    # 获取TCN输入投影层的权重
    tcn_weights = model.tcn.input_proj.weight.data.cpu().numpy()
    
    # 计算每个特征的平均绝对权重
    avg_weights = np.mean(np.abs(tcn_weights), axis=0)
    
    # 归一化
    total = np.sum(avg_weights)
    normalized_weights = avg_weights / total
    
    # 创建特征重要性字典
    importance_dict = {}
    for i, name in enumerate(feature_names):
        importance_dict[name] = float(normalized_weights[i])  # 确保转换为标量
    
    # 添加卡尔曼滤波的贡献
    kalman_features = [name for name in feature_names if 'kalman' in name or 'var' in name]
    kalman_importance = sum([importance_dict[name] for name in kalman_features])
    importance_dict['Kalman_Total'] = kalman_importance
    
    # 添加频域特征的贡献
    freq_features = [name for name in feature_names if 'freq' in name]
    freq_importance = sum([importance_dict[name] for name in freq_features])
    importance_dict['Frequency_Total'] = freq_importance
    
    return importance_dict


# 计算特征重要性
feature_importance = calculate_feature_importance(model, features)

# 可视化特征重要性
plt.figure(figsize=(12.8, 7.0))
sorted_importance = dict(sorted(feature_importance.items(), key=lambda item: item[1], reverse=True))

# 确保所有值都是标量
feature_names = list(sorted_importance.keys())
feature_values = [float(v) for v in sorted_importance.values()]

plt.bar(feature_names, feature_values, color='skyblue')
plt.title('特征在异常检测中的重要性')
plt.ylabel('相对重要性')
plt.xticks(rotation=45, ha='right')
plt.tight_layout()
plt.savefig('feature_importance_analysis.png', dpi=300)
plt.show()

print("TCN-xLSTM融合异常检测模型训练完成！")