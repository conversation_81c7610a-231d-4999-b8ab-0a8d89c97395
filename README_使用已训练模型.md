# 使用已训练模型进行异常检测

## 快速开始

现在您有两种方式使用已训练的模型：

### 方式1：使用专门的脚本（推荐）

运行 `use_trained_model.py`：

```bash
python use_trained_model.py
```

这个脚本会：
- ✅ 自动检查是否存在已训练的模型文件 `tcn_xlstm_fusion_model.pth`
- ✅ 直接加载模型，跳过训练过程
- ✅ 进行数据处理和异常检测
- ✅ 生成可视化结果

### 方式2：修改原始脚本

在原始脚本 `2025-08-04_Kalman_TCN_xLSTM_KnowledgeReasoning_as.py` 中：

1. 找到第392-394行的配置参数：
```python
# 模型控制参数 - 修改这里来控制是否训练
USE_PRETRAINED_MODEL = True  # 设置为True自动使用已训练模型，False强制重新训练
SKIP_TRAINING_PROMPT = True  # 设置为True跳过用户输入提示，直接使用已训练模型
```

2. 确保 `USE_PRETRAINED_MODEL = True`
3. 运行脚本：
```bash
python 2025-08-04_Kalman_TCN_xLSTM_KnowledgeReasoning_as.py
```

## 必要文件

确保以下文件存在于同一目录：
- `tcn_xlstm_fusion_model.pth` - 已训练的模型文件
- `725full.csv` - 数据文件

## 输出结果

运行后会生成：
- 控制台输出：模型性能指标和异常点统计
- `trained_model_results.png` - 可视化结果图片
- 异常点检测结果

## 性能优势

使用已训练模型的优势：
- ⚡ **快速**：跳过训练过程，直接进行推理
- 💾 **节省资源**：无需重新训练，节省计算资源
- 🎯 **一致性**：使用相同的训练参数和模型架构
- 📊 **即时结果**：快速获得异常检测结果

## 故障排除

如果遇到问题：

1. **模型文件不存在**：
   - 确保 `tcn_xlstm_fusion_model.pth` 文件在当前目录
   - 如果没有，需要先运行完整训练脚本生成模型

2. **数据文件不存在**：
   - 确保 `725full.csv` 文件在当前目录

3. **内存不足**：
   - 可以减小 `BATCH_SIZE` 参数

4. **CUDA错误**：
   - 脚本会自动检测并使用CPU，无需担心

## 自定义配置

可以修改以下参数来调整检测行为：

```python
# 异常检测阈值
z_threshold = 1.27  # Z-score阈值，越小检测越敏感

# 批处理大小
BATCH_SIZE = 32  # 可根据内存情况调整
```

## 注意事项

- 确保使用的数据格式与训练时一致
- 模型参数（TIME_STEPS, 特征数量等）必须与训练时相同
- 建议定期重新训练模型以适应新的数据模式
