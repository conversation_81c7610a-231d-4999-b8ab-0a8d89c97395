#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Author: 汪祥
# @Time : 2025/08/04 18:29:53
# @File : use_trained_model.py
# @Note : 直接使用已训练的模型进行异常检测，无需重新训练

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler, RobustScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error
from scipy import stats
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, TensorDataset
import seaborn as sns
from matplotlib import font_manager, rcParams
from scipy.signal import stft, welch
import time
import os

# 设置中文字体
font_path = '/System/Library/Fonts/Hiragino Sans GB.ttc'
font_prop = font_manager.FontProperties(fname=font_path, size=12)
rcParams['font.family'] = font_prop.get_name()
rcParams['axes.unicode_minus'] = False

# 设置随机种子保证可复现性
np.random.seed(42)
torch.manual_seed(42)

print("🚀 使用已训练模型进行异常检测")
print("=" * 50)

# 检查必要文件是否存在
model_path = 'tcn_xlstm_fusion_model.pth'
data_path = '725full.csv'

if not os.path.exists(model_path):
    print(f"❌ 错误：未找到训练好的模型文件 '{model_path}'")
    print("请先运行完整的训练脚本生成模型文件")
    exit(1)

if not os.path.exists(data_path):
    print(f"❌ 错误：未找到数据文件 '{data_path}'")
    exit(1)

print(f"✅ 找到模型文件: {model_path}")
print(f"✅ 找到数据文件: {data_path}")

# 导入必要的类定义（从原脚本复制）
class AdaptiveKalmanFilter:
    def __init__(self, initial_variance=1.0, min_process_variance=1e-6, max_process_variance=1e-2, 
                 min_measurement_variance=1e-4, max_measurement_variance=1.0, innovation_threshold=3.0):
        self.min_process_variance = min_process_variance
        self.max_process_variance = max_process_variance
        self.min_measurement_variance = min_measurement_variance
        self.max_measurement_variance = max_measurement_variance
        self.innovation_threshold = innovation_threshold
        
        self.posteri_estimate = 0.0
        self.posteri_error_estimate = initial_variance
        self.innovation_history = []
        self.measurement_variance_history = []
        self.process_variance_history = []
        
    def adaptive_variance(self, innovation, prev_measurement_var):
        abs_innovation = np.abs(innovation)
        
        if abs_innovation > self.innovation_threshold:
            measurement_variance = min(prev_measurement_var * 1.5, self.max_measurement_variance)
        else:
            measurement_variance = max(prev_measurement_var * 0.95, self.min_measurement_variance)
        
        process_variance = max(min(measurement_variance * 0.01, self.max_process_variance), self.min_process_variance)
        
        return process_variance, measurement_variance
    
    def update(self, measurement, prev_measurement_var):
        priori_estimate = self.posteri_estimate
        priori_error_estimate = self.posteri_error_estimate
        
        innovation = measurement - priori_estimate
        process_variance, measurement_variance = self.adaptive_variance(innovation, prev_measurement_var)
        
        kalman_gain = priori_error_estimate / (priori_error_estimate + measurement_variance)
        self.posteri_estimate = priori_estimate + kalman_gain * innovation
        self.posteri_error_estimate = (1 - kalman_gain) * priori_error_estimate + process_variance
        
        self.innovation_history.append(innovation)
        self.measurement_variance_history.append(measurement_variance)
        self.process_variance_history.append(process_variance)
        
        return self.posteri_estimate, kalman_gain, process_variance, measurement_variance

def apply_adaptive_kalman_filter(signal, initial_measurement_var=0.1, innovation_threshold=3.0):
    kf = AdaptiveKalmanFilter(initial_variance=1.0, innovation_threshold=innovation_threshold)
    filtered_signal = np.zeros_like(signal)
    kalman_gains = np.zeros_like(signal)
    process_variances = np.zeros_like(signal)
    measurement_variances = np.zeros_like(signal)
    
    measurement_var = initial_measurement_var
    
    for i, value in enumerate(signal):
        filtered_value, gain, proc_var, meas_var = kf.update(value, measurement_var)
        filtered_signal[i] = filtered_value
        kalman_gains[i] = gain
        process_variances[i] = proc_var
        measurement_variances[i] = meas_var
        measurement_var = meas_var
    
    state_features = np.column_stack((kalman_gains, process_variances, measurement_variances))
    return filtered_signal, state_features

def extract_enhanced_frequency_features(signal, window_size=256, nperseg=64):
    if len(signal) < window_size:
        window_size = len(signal) // 2
    
    freq_features = np.zeros((len(signal), 5))
    
    for i in range(len(signal) - window_size + 1):
        segment = signal[i:i+window_size]
        
        f, t, Zxx = stft(segment, nperseg=nperseg, boundary='even')
        magnitude = np.abs(Zxx)
        
        freqs, psd = welch(segment, fs=1000, nperseg=64)
        
        freq_features[i+window_size-1, 0] = np.mean(magnitude)
        freq_features[i+window_size-1, 1] = np.std(magnitude)
        freq_features[i+window_size-1, 2] = np.max(magnitude)
        
        if len(psd) > 0:
            freq_features[i+window_size-1, 3] = freqs[np.argmax(psd)]
            freq_features[i+window_size-1, 4] = np.sum(psd > np.max(psd)*0.5) / len(psd)
    
    for i in range(window_size - 1):
        freq_features[i] = freq_features[window_size - 1]
    
    return freq_features

# TCN和xLSTM模型定义
class TCNBlock(nn.Module):
    def __init__(self, in_channels, out_channels, kernel_size, dilation, dropout=0.2):
        super(TCNBlock, self).__init__()
        padding = (kernel_size - 1) * dilation // 2
        
        self.conv1 = nn.Conv1d(in_channels, out_channels, kernel_size, padding=padding, dilation=dilation)
        self.norm1 = nn.BatchNorm1d(out_channels)
        self.relu1 = nn.ReLU()
        self.dropout1 = nn.Dropout(dropout)
        
        self.conv2 = nn.Conv1d(out_channels, out_channels, kernel_size, padding=padding, dilation=dilation)
        self.norm2 = nn.BatchNorm1d(out_channels)
        self.relu2 = nn.ReLU()
        self.dropout2 = nn.Dropout(dropout)
        
        self.downsample = nn.Conv1d(in_channels, out_channels, 1) if in_channels != out_channels else None
        
    def forward(self, x):
        residual = x
        
        out = self.conv1(x)
        out = self.norm1(out)
        out = self.relu1(out)
        out = self.dropout1(out)
        
        out = self.conv2(out)
        out = self.norm2(out)
        
        if self.downsample is not None:
            residual = self.downsample(residual)
            
        if out.size(2) != residual.size(2):
            min_size = min(out.size(2), residual.size(2))
            out = out[:, :, :min_size]
            residual = residual[:, :, :min_size]
        
        out += residual
        out = self.relu2(out)
        out = self.dropout2(out)
        return out

class TCNModule(nn.Module):
    def __init__(self, input_size, tcn_channels, kernel_size, num_blocks, dropout=0.2):
        super(TCNModule, self).__init__()
        self.layers = nn.ModuleList()
        
        self.input_proj = nn.Conv1d(input_size, tcn_channels, 1)
        
        for i in range(num_blocks):
            dilation = 2 ** i
            self.layers.append(TCNBlock(tcn_channels, tcn_channels, kernel_size, dilation, dropout))
            
    def forward(self, x):
        x = x.permute(0, 2, 1)
        x = self.input_proj(x)
        
        for layer in self.layers:
            x = layer(x)
            
        return x.permute(0, 2, 1)

class xLSTMCell(nn.Module):
    def __init__(self, input_size, hidden_size):
        super(xLSTMCell, self).__init__()
        self.input_size = input_size
        self.hidden_size = hidden_size
        
        self.W_i = nn.Linear(input_size + hidden_size, hidden_size)
        self.W_f = nn.Linear(input_size + hidden_size, hidden_size)
        self.W_o = nn.Linear(input_size + hidden_size, hidden_size)
        self.W_g = nn.Linear(input_size + hidden_size, hidden_size)
        
        self.layer_norm_i = nn.LayerNorm(hidden_size)
        self.layer_norm_f = nn.LayerNorm(hidden_size)
        self.layer_norm_o = nn.LayerNorm(hidden_size)
        self.layer_norm_g = nn.LayerNorm(hidden_size)
        
    def forward(self, x, hidden):
        h_prev, c_prev = hidden
        
        combined = torch.cat([x, h_prev], dim=1)
        
        i_t = torch.sigmoid(self.layer_norm_i(self.W_i(combined)))
        f_t = torch.sigmoid(self.layer_norm_f(self.W_f(combined)))
        o_t = torch.sigmoid(self.layer_norm_o(self.W_o(combined)))
        g_t = torch.tanh(self.layer_norm_g(self.W_g(combined)))
        
        c_t = f_t * c_prev + i_t * g_t
        h_t = o_t * torch.tanh(c_t)
        
        return h_t, c_t

class TCN_xLSTM_Fusion(nn.Module):
    def __init__(self, input_size, tcn_channels, tcn_kernel_size, tcn_blocks, 
                 hidden_size, num_layers, output_size, dropout=0.2):
        super(TCN_xLSTM_Fusion, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        self.tcn = TCNModule(input_size, tcn_channels, kernel_size=tcn_kernel_size,
                            num_blocks=tcn_blocks, dropout=dropout)
        
        self.xlstm_layers = nn.ModuleList([
            xLSTMCell(tcn_channels if i == 0 else hidden_size, hidden_size)
            for i in range(num_layers)
        ])
        
        self.dropout = nn.Dropout(dropout)
        self.fc1 = nn.Linear(hidden_size, hidden_size // 2)
        self.fc2 = nn.Linear(hidden_size // 2, hidden_size // 4)
        self.fc3 = nn.Linear(hidden_size // 4, output_size)
        self.relu = nn.ReLU()
        self.interaction_fc = nn.Linear(hidden_size, hidden_size)
        
        self._init_weights()
        
    def _init_weights(self):
        for name, param in self.named_parameters():
            if 'weight' in name:
                if 'fc' in name or 'W_' in name:
                    nn.init.xavier_normal_(param)
                elif 'conv' in name:
                    nn.init.kaiming_normal_(param, mode='fan_out', nonlinearity='relu')
            elif 'bias' in name:
                nn.init.constant_(param, 0.0)
                
    def forward(self, x):
        batch_size, seq_len, feature_size = x.size()
        
        tcn_out = self.tcn(x)
        
        h = [torch.zeros(batch_size, self.hidden_size).to(x.device) for _ in range(self.num_layers)]
        c = [torch.zeros(batch_size, self.hidden_size).to(x.device) for _ in range(self.num_layers)]
        
        for t in range(seq_len):
            x_t = tcn_out[:, t, :]
            for layer in range(self.num_layers):
                if layer == 0:
                    h[layer], c[layer] = self.xlstm_layers[layer](x_t, (h[layer], c[layer]))
                else:
                    if layer == self.num_layers - 1:
                        interactive = self.relu(self.interaction_fc(h[layer-1]))
                        h[layer], c[layer] = self.xlstm_layers[layer](interactive, (h[layer], c[layer]))
                    else:
                        h[layer], c[layer] = self.xlstm_layers[layer](h[layer-1], (h[layer], c[layer]))
        
        output = self.dropout(h[-1])
        output = self.relu(self.fc1(output))
        output = self.relu(self.fc2(output))
        output = self.fc3(output)
        
        return output

print("📊 开始数据处理...")

# 参数配置（与训练时保持一致）
TIME_STEPS = 120
TEST_SIZE = 0.40
VAL_SIZE = 0.15
BATCH_SIZE = 32
FREQ_WINDOW_SIZE = 128
TCN_CHANNELS = 64
TCN_KERNEL_SIZE = 5
TCN_BLOCKS = 4

# 数据加载和处理函数
def load_and_fuse_data(tension_path, vibration_path, extract_freq_features=True, apply_kalman=True):
    tension_df = pd.read_csv(tension_path)
    tension_df = tension_df[['Tension']].rename(columns={'Tension': 'tension'})

    vibration_df = pd.read_csv(vibration_path)
    vibration_df = vibration_df[['WT1_Z']].rename(columns={'WT1_Z': 'vibration'})

    min_length = min(len(tension_df), len(vibration_df))
    tension_df = tension_df.iloc[:min_length]
    vibration_df = vibration_df.iloc[:min_length]

    fused_df = pd.DataFrame({
        'raw_tension': tension_df['tension'].values,
        'raw_vibration': vibration_df['vibration'].values
    })

    if apply_kalman:
        print("应用自适应卡尔曼滤波...")

        tension_filtered, tension_state_features = apply_adaptive_kalman_filter(
            tension_df['tension'].values,
            initial_measurement_var=0.1,
            innovation_threshold=1.0
        )

        vibration_filtered, vibration_state_features = apply_adaptive_kalman_filter(
            vibration_df['vibration'].values,
            initial_measurement_var=0.5,
            innovation_threshold=1.0
        )

        fused_df['tension'] = tension_filtered
        fused_df['tension_kalman_gain'] = tension_state_features[:, 0]
        fused_df['tension_process_var'] = tension_state_features[:, 1]
        fused_df['tension_measure_var'] = tension_state_features[:, 2]

        fused_df['vibration'] = vibration_filtered
        fused_df['vibration_kalman_gain'] = vibration_state_features[:, 0]
        fused_df['vibration_process_var'] = vibration_state_features[:, 1]
        fused_df['vibration_measure_var'] = vibration_state_features[:, 2]
    else:
        fused_df['tension'] = fused_df['raw_tension']
        fused_df['vibration'] = fused_df['raw_vibration']

    fused_df['interaction'] = fused_df['tension'] * fused_df['vibration']

    if extract_freq_features:
        print("提取增强的振动频域特征...")
        vibration_signal = fused_df['vibration'].values
        freq_features = extract_enhanced_frequency_features(vibration_signal, window_size=FREQ_WINDOW_SIZE)

        fused_df['freq_mean'] = freq_features[:, 0]
        fused_df['freq_std'] = freq_features[:, 1]
        fused_df['freq_max'] = freq_features[:, 2]
        fused_df['freq_dominant'] = freq_features[:, 3]
        fused_df['freq_bandwidth'] = freq_features[:, 4]

    return fused_df

# 创建监督学习数据集
def create_dataset(data, time_steps=1):
    X, y = [], []
    for i in range(len(data) - time_steps):
        X.append(data[i:(i + time_steps), :])
        y.append(data[i + time_steps, :2])
    return np.array(X), np.array(y)

# 异常检测函数
def detect_enhanced_anomalies(model, dataloader, scalers, device, z_threshold=1.27):
    model.eval()
    predictions = []
    actuals = []

    with torch.no_grad():
        for batch_x, batch_y in dataloader:
            batch_x, batch_y = batch_x.to(device), batch_y.to(device)
            outputs = model(batch_x)

            predictions.append(outputs.cpu().numpy())
            actuals.append(batch_y.cpu().numpy())

    predictions = np.concatenate(predictions, axis=0)
    actuals = np.concatenate(actuals, axis=0)

    # 反归一化
    tension_pred_rescaled = scalers['tension'].inverse_transform(predictions[:, 0].reshape(-1, 1)).flatten()
    vibration_pred_rescaled = scalers['vibration'].inverse_transform(predictions[:, 1].reshape(-1, 1)).flatten()

    tension_actual_rescaled = scalers['tension'].inverse_transform(actuals[:, 0].reshape(-1, 1)).flatten()
    vibration_actual_rescaled = scalers['vibration'].inverse_transform(actuals[:, 1].reshape(-1, 1)).flatten()

    # 计算误差
    tension_errors = np.abs(tension_pred_rescaled - tension_actual_rescaled)
    vibration_errors = np.abs(vibration_pred_rescaled - vibration_actual_rescaled)

    # 自适应权重
    tension_std = np.std(tension_errors)
    vibration_std = np.std(vibration_errors)

    tension_weight = vibration_std / (tension_std + vibration_std + 1e-8)
    vibration_weight = tension_std / (tension_std + vibration_std + 1e-8)

    total_weight = tension_weight + vibration_weight
    tension_weight /= total_weight
    vibration_weight /= total_weight

    print(f"自适应权重 - 张力: {tension_weight:.4f}, 振动: {vibration_weight:.4f}")

    # 计算融合误差
    fused_errors = (tension_weight * tension_errors) + (vibration_weight * vibration_errors)

    # 计算Z-score
    z_scores = np.abs(stats.zscore(fused_errors))
    anomaly_indices = np.where(z_scores > z_threshold)[0]

    # 动态阈值
    dynamic_threshold = np.median(z_scores) + 2.5 * stats.median_abs_deviation(z_scores)

    return (tension_pred_rescaled, vibration_pred_rescaled,
            tension_actual_rescaled, vibration_actual_rescaled,
            fused_errors, anomaly_indices, z_scores, dynamic_threshold,
            tension_weight, vibration_weight)

# 加载和处理数据
print("加载和融合数据...")
fused_df = load_and_fuse_data(data_path, data_path, extract_freq_features=True, apply_kalman=True)

features = [
    'tension', 'vibration', 'interaction',
    'tension_kalman_gain', 'tension_process_var', 'tension_measure_var',
    'vibration_kalman_gain', 'vibration_process_var', 'vibration_measure_var',
    'freq_mean', 'freq_std', 'freq_max', 'freq_dominant', 'freq_bandwidth'
]
print(f"使用的特征 ({len(features)}个): {features}")

# 数据归一化
print("数据归一化处理...")
scalers = {}
scaled_features = []

for feature in features:
    if 'kalman' in feature or 'var' in feature:
        scaler = RobustScaler()
    else:
        scaler = MinMaxScaler(feature_range=(-1, 1))

    scaled_feature = scaler.fit_transform(fused_df[[feature]].values)
    scalers[feature] = scaler
    scaled_features.append(scaled_feature)

scaled_data = np.concatenate(scaled_features, axis=1)

print("创建监督学习数据集...")
X, y = create_dataset(scaled_data, TIME_STEPS)

# 划分数据集（与训练时保持一致）
train_size = int(len(X) * (1 - TEST_SIZE - VAL_SIZE))
val_size = int(len(X) * VAL_SIZE)

X_test = X[train_size+val_size:]
y_test = y[train_size+val_size:]

X_test = torch.FloatTensor(X_test)
y_test = torch.FloatTensor(y_test)

test_dataset = TensorDataset(X_test, y_test)
test_loader = DataLoader(test_dataset, batch_size=BATCH_SIZE, shuffle=False)

print(f"测试集形状: {X_test.shape}")
print(f"输入特征数: {X_test.shape[-1]}")

# 加载模型
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")

model = TCN_xLSTM_Fusion(
    input_size=X_test.shape[-1],
    tcn_channels=TCN_CHANNELS,
    tcn_kernel_size=TCN_KERNEL_SIZE,
    tcn_blocks=TCN_BLOCKS,
    hidden_size=128,
    num_layers=2,
    output_size=2,
    dropout=0.2
).to(device)

print("🔄 加载已训练的模型...")
model.load_state_dict(torch.load(model_path, map_location=device))
model.eval()
print("✅ 模型加载成功！")

# 进行异常检测
print("🔍 开始异常检测...")
(tension_pred, vibration_pred,
 tension_actual, vibration_actual,
 fused_errors, test_anomalies,
 test_z_scores, dynamic_threshold,
 tension_weight, vibration_weight) = detect_enhanced_anomalies(
    model, test_loader, scalers, device, z_threshold=1.27
)

# 模型评估
tension_rmse = np.sqrt(mean_squared_error(tension_actual, tension_pred))
tension_mae = mean_absolute_error(tension_actual, tension_pred)

vibration_rmse = np.sqrt(mean_squared_error(vibration_actual, vibration_pred))
vibration_mae = mean_absolute_error(vibration_actual, vibration_pred)

print("\n📈 模型性能评估:")
print(f"张力测试集RMSE: {tension_rmse:.4f}, MAE: {tension_mae:.4f}")
print(f"振动测试集RMSE: {vibration_rmse:.4f}, MAE: {vibration_mae:.4f}")
print(f"异常点数量: {len(test_anomalies)}/{len(tension_pred)} ({len(test_anomalies)/len(tension_pred)*100:.2f}%)")
print(f"动态阈值: {dynamic_threshold:.4f}")

# 可视化结果
print("\n📊 生成可视化结果...")
plt.figure(figsize=(12.8, 7.0))

# 张力预测结果对比
plt.subplot(3, 1, 1)
plt.plot(tension_actual, label='张力真实值', color='blue', alpha=0.7)
plt.plot(tension_pred, label='张力预测值', color='orange', alpha=0.7, linestyle='--')

if len(test_anomalies) > 0:
    plt.scatter(test_anomalies, tension_actual[test_anomalies],
                color='red', s=70, zorder=5, label='异常点')

plt.title('使用已训练模型的张力预测结果')
plt.ylabel('张力值(KN)')
plt.legend()
plt.grid(True, linestyle='--', alpha=0.7)
plt.xticks(range(0, len(tension_actual), 80))

# 振动预测结果对比
plt.subplot(3, 1, 2)
plt.plot(vibration_actual, label='振动真实值', color='green', alpha=0.7)
plt.plot(vibration_pred, label='振动预测值', color='purple', alpha=0.7, linestyle='--')

if len(test_anomalies) > 0:
    plt.scatter(test_anomalies, vibration_actual[test_anomalies],
                color='red', s=70, zorder=5, label='异常点')

plt.title('使用已训练模型的振动预测结果')
plt.ylabel('振动值(G)')
plt.legend()
plt.grid(True, linestyle='--', alpha=0.7)
plt.xticks(range(0, len(vibration_actual), 80))

# 融合误差和Z-score可视化
plt.subplot(3, 1, 3)
plt.plot(fused_errors, color='blue', alpha=0.6, label='融合误差')
plt.plot(test_z_scores, color='green', label='Z-score', linewidth=2)
plt.axhline(y=1.27, color='red', linestyle='--', label='异常阈值 (Z=1.27)')
plt.axhline(y=dynamic_threshold, color='purple', linestyle='-.', label=f'动态阈值 ({dynamic_threshold:.2f})')

if len(test_anomalies) > 0:
    plt.scatter(test_anomalies, test_z_scores[test_anomalies],
                color='red', s=70, zorder=5, label='异常点')

plt.title('多物理量融合异常点检测结果')
plt.ylabel('融合误差/Z-score')
plt.legend()
plt.grid(True, linestyle='--', alpha=0.7)
plt.xticks(range(0, len(fused_errors), 80))

plt.tight_layout()
plt.savefig('trained_model_results.png', dpi=300)
plt.show()

print(f"\n✅ 异常检测完成！")
print(f"📁 结果图片已保存为: trained_model_results.png")
print(f"🎯 检测到 {len(test_anomalies)} 个异常点")

if len(test_anomalies) > 0:
    print(f"🔍 异常点位置: {test_anomalies[:10]}{'...' if len(test_anomalies) > 10 else ''}")

print("\n🎉 程序运行完成！")
